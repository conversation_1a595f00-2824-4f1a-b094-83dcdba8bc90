import React, { useState } from 'react';
import { batchGetCountriesFromIPs } from '../utils/geolocation';
import { API_CONFIG } from '../config/apiConfig';

const GeolocationTester = () => {
  const [testResults, setTestResults] = useState({});
  const [monitoringStats, setMonitoringStats] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Enhanced test IPs covering priority regions
  const testIPs = [
    '**************', // Tunisia (specific IP from requirements)
    '**************', // Tunisia (from console logs)
    '*************',  // Algeria
    '*************',  // Morocco
    '*************',  // Saudi Arabia
    '*************',  // UAE
    '************',   // Germany
    '***********',    // France
    '*******',        // US (Google DNS)
    '127.0.0.1',      // Local
    '999.999.999.999' // Invalid IP for testing
  ];

  const runTest = async () => {
    setLoading(true);
    setError('');
    setTestResults({});
    setMonitoringStats(null);

    try {
      console.log('🧪 Starting enhanced geolocation test...');
      const results = await batchGetCountriesFromIPs(testIPs);

      console.log('🧪 Test results:', results);
      setTestResults(results);

      // Log specific results for debugging
      testIPs.forEach(ip => {
        const result = results[ip];
        const source = result?.source ? ` (${result.source})` : '';
        console.log(`🧪 ${ip} → ${result?.country || 'No result'} ${result?.flag || '❓'}${source}`);
      });

      // Fetch monitoring stats
      await fetchMonitoringStats();

    } catch (err) {
      console.error('🧪 Test failed:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchMonitoringStats = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await fetch(API_CONFIG.ENDPOINTS.GEOLOCATION_MONITORING, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setMonitoringStats(data.monitoring);
        }
      }
    } catch (error) {
      console.warn('Failed to fetch monitoring stats:', error);
    }
  };

  const getExpectedResult = (ip) => {
    if (ip.startsWith('102.157.') || ip.startsWith('41.227.')) return 'Tunisia 🇹🇳';
    if (ip.startsWith('105.100.')) return 'Algeria 🇩🇿';
    if (ip.startsWith('41.250.')) return 'Morocco 🇲🇦';
    if (ip.startsWith('188.161.')) return 'Saudi Arabia 🇸🇦';
    if (ip.startsWith('213.42.')) return 'United Arab Emirates 🇦🇪';
    if (ip.startsWith('84.') || ip.startsWith('176.143.')) return 'Germany 🇩🇪 or France 🇫🇷';
    if (ip === '*******') return 'United States 🇺🇸';
    if (ip === '127.0.0.1') return 'Local 🏠';
    return 'Unknown 🌍';
  };

  const isCorrect = (ip, result) => {
    const expected = getExpectedResult(ip);
    const actual = `${result?.country || 'Unknown'} ${result?.flag || '🌍'}`;
    return expected === actual;
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h2>🧪 Geolocation Testing Tool</h2>
      <p>This tool tests if the IP geolocation fixes are working correctly.</p>
      
      <button 
        onClick={runTest} 
        disabled={loading}
        style={{
          padding: '10px 20px',
          backgroundColor: '#FF2D55',
          color: 'white',
          border: 'none',
          borderRadius: '5px',
          cursor: loading ? 'not-allowed' : 'pointer',
          marginBottom: '20px'
        }}
      >
        {loading ? 'Testing...' : 'Run Geolocation Test'}
      </button>

      {error && (
        <div style={{ color: 'red', marginBottom: '20px' }}>
          ❌ Error: {error}
        </div>
      )}

      {Object.keys(testResults).length > 0 && (
        <div>
          <h3>📊 Test Results</h3>
          <table style={{ width: '100%', borderCollapse: 'collapse' }}>
            <thead>
              <tr style={{ backgroundColor: '#f5f5f5' }}>
                <th style={{ padding: '10px', border: '1px solid #ddd' }}>IP Address</th>
                <th style={{ padding: '10px', border: '1px solid #ddd' }}>Expected</th>
                <th style={{ padding: '10px', border: '1px solid #ddd' }}>Actual Result</th>
                <th style={{ padding: '10px', border: '1px solid #ddd' }}>Status</th>
              </tr>
            </thead>
            <tbody>
              {testIPs.map(ip => {
                const result = testResults[ip];
                const correct = isCorrect(ip, result);
                return (
                  <tr key={ip}>
                    <td style={{ padding: '10px', border: '1px solid #ddd', fontFamily: 'monospace' }}>
                      {ip}
                    </td>
                    <td style={{ padding: '10px', border: '1px solid #ddd' }}>
                      {getExpectedResult(ip)}
                    </td>
                    <td style={{ padding: '10px', border: '1px solid #ddd' }}>
                      {result ? `${result.country} ${result.flag}` : 'No result'}
                      {result?.source && <div style={{ fontSize: '12px', color: '#0066cc' }}>
                        Source: {result.source}
                      </div>}
                      {result?.error && <div style={{ fontSize: '12px', color: '#666' }}>
                        Error: {result.errorMessage}
                      </div>}
                    </td>
                    <td style={{ padding: '10px', border: '1px solid #ddd' }}>
                      {result ? (correct ? '✅ Correct' : '❌ Incorrect') : '❓ No data'}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
          
          <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f0f8ff', borderRadius: '5px' }}>
            <h4>🎯 Enhanced Test Coverage:</h4>
            <ul>
              <li><strong>Tunisia:</strong> **************, ************** should show 🇹🇳</li>
              <li><strong>Algeria:</strong> ************* should show 🇩🇿</li>
              <li><strong>Morocco:</strong> ************* should show 🇲🇦</li>
              <li><strong>Saudi Arabia:</strong> ************* should show 🇸🇦</li>
              <li><strong>UAE:</strong> ************* should show 🇦🇪</li>
              <li><strong>Germany/France:</strong> ************, *********** should show 🇩🇪/🇫🇷</li>
              <li>Sources should show: ip_database, ipinfo.io, or ipwhois.io</li>
            </ul>
          </div>

          {monitoringStats && (
            <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '5px' }}>
              <h4>📊 System Monitoring Stats:</h4>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '15px' }}>
                <div>
                  <strong>IP Database:</strong>
                  <div>Success Rate: {monitoringStats.ipDatabase.successRate}</div>
                  <div>Calls: {monitoringStats.ipDatabase.success + monitoringStats.ipDatabase.failure}</div>
                </div>
                <div>
                  <strong>ipinfo.io API:</strong>
                  <div>Success Rate: {monitoringStats.ipinfoApi.successRate}</div>
                  <div>Rate Limited: {monitoringStats.ipinfoApi.rateLimited}</div>
                </div>
                <div>
                  <strong>ipwhois.io API:</strong>
                  <div>Success Rate: {monitoringStats.ipwhoisApi.successRate}</div>
                  <div>Rate Limited: {monitoringStats.ipwhoisApi.rateLimited}</div>
                </div>
                <div>
                  <strong>Cache Performance:</strong>
                  <div>Hit Rate: {monitoringStats.cache.hitRate}</div>
                  <div>Size: {monitoringStats.cacheSize} entries</div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default GeolocationTester;

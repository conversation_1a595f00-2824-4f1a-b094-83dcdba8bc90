// Quick test to check if deployment worked
const https = require('https');

function makeRequest(options, postData = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (e) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', (e) => {
      reject(e);
    });

    if (postData) {
      req.write(postData);
    }
    req.end();
  });
}

async function quickTest() {
  try {
    console.log('🔍 Quick deployment test...');
    
    // Login
    const loginOptions = {
      hostname: 'porfolio-pro-backend.onrender.com',
      port: 443,
      path: '/api/admin/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const loginData = JSON.stringify({
      email: '<EMAIL>',
      password: 'Adminboss'
    });

    const loginResponse = await makeRequest(loginOptions, loginData);
    
    if (loginResponse.status !== 200) {
      throw new Error(`Login failed: ${JSON.stringify(loginResponse.data)}`);
    }

    const token = loginResponse.data.token;
    console.log('✅ Login successful');

    // Test Experience Analytics
    const experienceOptions = {
      hostname: 'porfolio-pro-backend.onrender.com',
      port: 443,
      path: '/api/admin/experience-projects-analytics',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    const experienceResponse = await makeRequest(experienceOptions);
    
    if (experienceResponse.status === 200) {
      console.log(`📊 Experience Projects: ${experienceResponse.data.totalProjects}`);
      
      if (experienceResponse.data.data && experienceResponse.data.data.length > 0) {
        const firstProject = experienceResponse.data.data[0];
        console.log(`🔍 First project: ${firstProject.jobTitle}`);
        console.log(`🏢 Company: ${firstProject.company || 'N/A'}`);
        console.log(`🖼️ Logo: ${firstProject.logo || 'N/A'}`);
        
        if (firstProject.company && firstProject.logo) {
          console.log('✅ DEPLOYMENT SUCCESSFUL - Company and logo data present!');
        } else {
          console.log('❌ DEPLOYMENT NOT YET COMPLETE - Still showing old data');
        }
      }
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

quickTest();

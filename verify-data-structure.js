// Verify that our data structures match what we expect

// Experience Projects (from jobsData) - ONLY 2 projects
const actualExperienceProjects = [
  {
    id: 1,
    slug: "frontend-receeto",
    title: "Frontend Developer Angular",
    company: "Company : Receeto",
    logo: "/Receeto_logo.jpg" // hero-company-logo
  },
  {
    id: 2,
    slug: "3d-ecommerce-developer", 
    title: "3D E-commerce Developer",
    company: "Company : Freelance",
    logo: "/3D E-Comm.PNG" // hero-company-logo
  }
];

// Portfolio Projects (from portfolio-carousel) - 7 projects
const actualPortfolioProjects = [
  { 
    id: "portfolio-1",
    href: "https://threed-e-commerce.onrender.com",
    image: "/3D E-Comm.PNG",
    alt: "3D Ecommerce",
    title: "3D Ecommerce",
    forSale: true
  },
  { 
    id: "portfolio-2",
    href: "#",
    image: "/ex1.webp",
    alt: "Will be deployed soon.",
    title: "Will be deployed soon.",
    forSale: false
  },
  { 
    id: "portfolio-3",
    href: "https://creative-website-jumper.onrender.com",
    image: "/P1.PNG",
    alt: "Nexit Brand Identity",
    title: "Professional Portfolio",
    forSale: true
  },
  { 
    id: "portfolio-4",
    href: "#",
    image: "/ex3.webp",
    alt: "Will be deployed soon.s",
    title: "Will be deployed soon.",
    forSale: false
  },
  { 
    id: "portfolio-5",
    href: "https://flawless-2pqq.onrender.com",
    image: "/Flaw.PNG",
    alt: "Flaw",
    title: "Available",
    forSale: true
  },
  { 
    id: "portfolio-6",
    href: "#",
    image: "/ex5.png",
    alt: "Will be deployed soon.",
    title: "Will be deployed soon.",
    forSale: false
  },
  { 
    id: "portfolio-7",
    href: "https://hoobank-neon-future.onrender.com",
    image: "/HooBank.png",
    alt: "Business Web UI",
    title: "Experience digital banking with AI ",
    forSale: true
  }
];

console.log('✅ Experience Projects Data Structure:');
console.log(`📊 Total Experience Projects: ${actualExperienceProjects.length}`);
actualExperienceProjects.forEach((project, index) => {
  console.log(`  ${index + 1}. ${project.title}`);
  console.log(`     Company: ${project.company}`);
  console.log(`     Logo (hero-company-logo): ${project.logo}`);
  console.log(`     Slug: ${project.slug}`);
  console.log('');
});

console.log('✅ Portfolio Projects Data Structure:');
console.log(`📊 Total Portfolio Projects: ${actualPortfolioProjects.length}`);
actualPortfolioProjects.forEach((project, index) => {
  console.log(`  ${index + 1}. ${project.title}`);
  console.log(`     ID: ${project.id}`);
  console.log(`     Image: ${project.image}`);
  console.log(`     Available: ${project.href !== '#' ? 'Yes' : 'No'}`);
  console.log(`     URL: ${project.href}`);
  console.log('');
});

console.log('🎯 Expected Results:');
console.log('- Experience Projects: Show ONLY 2 projects with company logos');
console.log('- Portfolio Projects: Show ONLY 7 projects with images');
console.log('- NO duplicates');
console.log('- Sorted by most viewers/visitors');
console.log('- Images from hero-company-logo and project images displayed');

// 🛡️ BULLETPROOF GEOLOCATION VALIDATION TEST
// Tests 100% coverage ensuring NO IP EVER shows as "Unknown"

const https = require('https');

// Configuration
const API_URL = 'https://porfolio-pro-backend.onrender.com';
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = 'Adminboss';

// Comprehensive test IPs from every continent and major countries
const GLOBAL_TEST_IPS = [
  // Africa
  { ip: '**************', continent: 'Africa', expected: 'Tunisia', priority: true },
  { ip: '**************', continent: 'Africa', expected: 'Tunisia', priority: true },
  { ip: '*************', continent: 'Africa', expected: 'Algeria', priority: true },
  { ip: '*************', continent: 'Africa', expected: 'Morocco', priority: true },
  { ip: '*************', continent: 'Africa', expected: 'North Africa' },
  { ip: '************', continent: 'Africa', expected: 'North Africa' },
  
  // Middle East
  { ip: '*************', continent: 'Asia', expected: 'Saudi Arabia', priority: true },
  { ip: '*************', continent: 'Asia', expected: 'UAE', priority: true },
  { ip: '***********', continent: 'Asia', expected: 'Middle East' },
  { ip: '************', continent: 'Asia', expected: 'Middle East' },
  
  // Europe
  { ip: '************', continent: 'Europe', expected: 'Germany', priority: true },
  { ip: '*************', continent: 'Europe', expected: 'France', priority: true },
  { ip: '************', continent: 'Europe', expected: 'Europe' },
  { ip: '************', continent: 'Europe', expected: 'Europe' },
  { ip: '***********', continent: 'Europe', expected: 'Europe' },
  
  // North America
  { ip: '*******', continent: 'North America', expected: 'United States' },
  { ip: '*******', continent: 'North America', expected: 'United States' },
  { ip: '*******', continent: 'North America', expected: 'United States' },
  { ip: '************', continent: 'North America', expected: 'Canada' },
  { ip: '************', continent: 'North America', expected: 'North America' },
  
  // Asia-Pacific
  { ip: '*******', continent: 'Asia', expected: 'China' },
  { ip: '************', continent: 'Asia', expected: 'China' },
  { ip: '************', continent: 'Asia', expected: 'Asia-Pacific' },
  { ip: '************', continent: 'Asia', expected: 'China' },
  { ip: '************', continent: 'Asia', expected: 'China' },
  { ip: '*************', continent: 'Asia', expected: 'Asia-Pacific' },
  { ip: '*************', continent: 'Asia', expected: 'China' },
  { ip: '*************', continent: 'Asia', expected: 'China' },
  { ip: '*************', continent: 'Asia', expected: 'Japan' },
  { ip: '*************', continent: 'Asia', expected: 'Japan' },
  { ip: '20***********', continent: 'Asia', expected: 'Asia-Pacific' },
  { ip: '*************', continent: 'Asia', expected: 'Asia-Pacific' },
  
  // Australia/Oceania
  { ip: '************', continent: 'Oceania', expected: 'Australia' },
  { ip: '************', continent: 'Oceania', expected: 'Australia' },
  { ip: '*************', continent: 'Oceania', expected: 'Australia' },
  { ip: '*************', continent: 'Oceania', expected: 'Australia' },
  
  // South America
  { ip: '*************', continent: 'South America', expected: 'Brazil' },
  { ip: '*************', continent: 'South America', expected: 'Brazil' },
  { ip: '*************', continent: 'South America', expected: 'Brazil' },
  
  // Edge cases and unusual ranges
  { ip: '*******', continent: 'Unknown', expected: 'Any Country' },
  { ip: '***********', continent: 'Unknown', expected: 'Any Country' },
  { ip: '***********', continent: 'Unknown', expected: 'Any Country' },
  { ip: '***********', continent: 'Unknown', expected: 'Any Country' },
  { ip: '***********', continent: 'Unknown', expected: 'Any Country' },
  { ip: '***********', continent: 'Unknown', expected: 'Any Country' },
  { ip: '***************', continent: 'Unknown', expected: 'Any Country' },
  { ip: '***************', continent: 'Unknown', expected: 'Any Country' },
  
  // Local/Private (should always work)
  { ip: '127.0.0.1', continent: 'Local', expected: 'Local' },
  { ip: '***********', continent: 'Local', expected: 'Local' },
  { ip: '********', continent: 'Local', expected: 'Local' },
];

/**
 * Make HTTP request
 */
const makeRequest = (options, data = null) => {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({ status: res.statusCode, data: parsed });
        } catch (error) {
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
};

/**
 * Login and get JWT token
 */
const login = async () => {
  console.log('🔐 Authenticating for bulletproof test...');
  
  const options = {
    hostname: API_URL.replace('https://', ''),
    path: '/api/admin/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  const loginData = {
    email: ADMIN_EMAIL,
    password: ADMIN_PASSWORD
  };
  
  const response = await makeRequest(options, loginData);
  
  if (response.status === 200 && response.data.token) {
    console.log('✅ Authentication successful');
    return response.data.token;
  } else {
    throw new Error(`Authentication failed: ${response.status}`);
  }
};

/**
 * Test bulletproof geolocation coverage
 */
const testBulletproofCoverage = async (token) => {
  console.log('\n🛡️ Testing Bulletproof 100% Coverage...');
  console.log(`Testing ${GLOBAL_TEST_IPS.length} IPs from all continents`);
  
  const options = {
    hostname: API_URL.replace('https://', ''),
    path: '/api/admin/geolocation',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  };
  
  const testIPs = GLOBAL_TEST_IPS.map(item => item.ip);
  const requestData = { ips: testIPs };
  
  const response = await makeRequest(options, requestData);
  
  if (response.status === 200 && response.data.success) {
    return response.data.data;
  } else {
    throw new Error(`Geolocation request failed: ${response.status}`);
  }
};

/**
 * Validate 100% coverage (NO "Unknown" results allowed)
 */
const validate100Coverage = (results) => {
  console.log('\n🎯 BULLETPROOF VALIDATION RESULTS');
  console.log('==================================');
  
  let totalTests = 0;
  let successfulTests = 0;
  let unknownResults = 0;
  let errorResults = 0;
  
  const continentStats = {};
  const failedIPs = [];
  
  GLOBAL_TEST_IPS.forEach(testCase => {
    const result = results[testCase.ip];
    totalTests++;
    
    // Initialize continent stats
    if (!continentStats[testCase.continent]) {
      continentStats[testCase.continent] = { total: 0, success: 0, unknown: 0, error: 0 };
    }
    continentStats[testCase.continent].total++;
    
    let status = '❌';
    let reason = 'No result';
    
    if (result) {
      if (result.country === 'Unknown' || result.country_code === 'UN') {
        unknownResults++;
        continentStats[testCase.continent].unknown++;
        status = '🚨';
        reason = `CRITICAL: Shows as "Unknown"`;
        failedIPs.push({ ip: testCase.ip, reason, continent: testCase.continent });
      } else if (result.error) {
        errorResults++;
        continentStats[testCase.continent].error++;
        status = '⚠️';
        reason = `Error: ${result.errorMessage}`;
        failedIPs.push({ ip: testCase.ip, reason, continent: testCase.continent });
      } else {
        successfulTests++;
        continentStats[testCase.continent].success++;
        status = '✅';
        reason = `${result.country} ${result.flag} (${result.source || 'unknown'})`;
      }
    } else {
      continentStats[testCase.continent].error++;
      failedIPs.push({ ip: testCase.ip, reason, continent: testCase.continent });
    }
    
    console.log(`${status} ${testCase.ip} (${testCase.continent}) → ${reason}`);
  });
  
  // Summary by continent
  console.log('\n📊 COVERAGE BY CONTINENT:');
  Object.entries(continentStats).forEach(([continent, stats]) => {
    const successRate = ((stats.success / stats.total) * 100).toFixed(1);
    const unknownRate = ((stats.unknown / stats.total) * 100).toFixed(1);
    console.log(`   ${continent}: ${successRate}% success, ${unknownRate}% unknown (${stats.success}/${stats.total})`);
  });
  
  // Overall results
  const successRate = ((successfulTests / totalTests) * 100).toFixed(1);
  const unknownRate = ((unknownResults / totalTests) * 100).toFixed(1);
  
  console.log('\n🎯 OVERALL BULLETPROOF RESULTS:');
  console.log(`   Total Tests: ${totalTests}`);
  console.log(`   Successful: ${successfulTests} (${successRate}%)`);
  console.log(`   Unknown Results: ${unknownResults} (${unknownRate}%)`);
  console.log(`   Error Results: ${errorResults}`);
  
  // Critical assessment
  const isBulletproof = unknownResults === 0;
  
  console.log(`\n🛡️ BULLETPROOF STATUS: ${isBulletproof ? '✅ ACHIEVED' : '🚨 FAILED'}`);
  
  if (!isBulletproof) {
    console.log('\n🚨 CRITICAL ISSUES FOUND:');
    failedIPs.forEach(failure => {
      console.log(`   ❌ ${failure.ip} (${failure.continent}): ${failure.reason}`);
    });
    console.log('\n⚠️ System is NOT bulletproof - some IPs still show as "Unknown"');
  } else {
    console.log('\n🎉 SUCCESS: 100% bulletproof coverage achieved!');
    console.log('   ✅ NO IPs show as "Unknown"');
    console.log('   ✅ All continents covered');
    console.log('   ✅ System is production-ready');
  }
  
  return {
    isBulletproof,
    successRate: parseFloat(successRate),
    unknownRate: parseFloat(unknownRate),
    totalTests,
    successfulTests,
    unknownResults,
    errorResults,
    continentStats,
    failedIPs
  };
};

/**
 * Main bulletproof validation
 */
const runBulletproofValidation = async () => {
  console.log('🛡️ BULLETPROOF GEOLOCATION VALIDATION');
  console.log('=====================================');
  console.log('Testing 100% coverage - NO "Unknown" results allowed');
  
  try {
    // Step 1: Login
    const token = await login();
    
    // Step 2: Test comprehensive coverage
    const results = await testBulletproofCoverage(token);
    
    // Step 3: Validate 100% coverage
    const validation = validate100Coverage(results);
    
    // Final verdict
    if (validation.isBulletproof) {
      console.log('\n🏆 BULLETPROOF CERTIFICATION: ✅ PASSED');
      console.log('Your geolocation system is 100% bulletproof!');
    } else {
      console.log('\n🚨 BULLETPROOF CERTIFICATION: ❌ FAILED');
      console.log('System needs improvements to achieve 100% coverage.');
    }
    
    return validation;
    
  } catch (error) {
    console.error('❌ Bulletproof validation failed:', error.message);
    return { isBulletproof: false, error: error.message };
  }
};

// Run the bulletproof validation
runBulletproofValidation();

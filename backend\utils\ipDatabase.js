// Comprehensive IP Range Database for Geolocation
// This provides extensive IP range coverage without relying on external APIs

/**
 * IP Range Database - Organized by Country
 * Sources: IANA, AFRINIC, RIPE, and other regional registries
 */
const IP_RANGES = {
  // Tunisia - Comprehensive Coverage
  TN: {
    country: 'Tunisia',
    country_code: 'TN',
    city: 'Tunis',
    region: 'Tunis',
    timezone: 'Africa/Tunis',
    isp: 'Tunisia Telecom',
    ranges: [
      // Primary Tunisia ranges
      '41.224.', '41.225.', '41.226.', '41.227.', '41.228.', '41.229.',
      '41.230.', '41.231.', '41.232.', '41.233.', '41.234.', '41.235.',
      '41.236.', '41.237.', '41.238.', '41.239.', '41.240.', '41.241.',
      '41.242.', '41.243.', '41.244.', '41.245.', '41.246.', '41.247.',
      '41.248.', '41.249.', '41.250.', '41.251.', '41.252.', '41.253.',
      
      // 102.x.x.x ranges (AFRINIC allocation)
      '102.140.', '102.141.', '102.142.', '102.143.', '102.144.', '102.145.',
      '102.146.', '102.147.', '102.148.', '102.149.', '102.150.', '102.151.',
      '102.152.', '102.153.', '102.154.', '102.155.', '102.156.', '102.157.',
      '102.158.', '102.159.', '102.160.', '102.161.', '102.162.', '102.163.',
      '102.164.', '102.165.', '102.166.', '102.167.', '102.168.', '102.169.',
      '102.170.', '102.171.', '102.172.', '102.173.', '102.174.', '102.175.',
      '102.176.', '102.177.', '102.178.', '102.179.', '102.180.', '102.181.',
      '102.182.', '102.183.', '102.184.', '102.185.', '102.186.', '102.187.',
      '102.188.', '102.189.', '102.190.', '102.191.', '102.192.', '102.193.',
      '102.194.', '102.195.', '102.196.', '102.197.', '102.198.', '102.199.',
      '102.200.', '102.24.',
      
      // 197.x.x.x ranges (North Africa)
      '197.14.', '197.15.', '197.16.', '197.17.', '197.18.', '197.19.',
      '197.20.', '197.21.', '197.22.', '197.23.', '197.24.', '197.25.',
      
      // 196.x.x.x ranges (Africa)
      '196.200.', '196.201.', '196.202.', '196.203.', '196.204.', '196.205.',
      '196.206.', '196.207.', '196.208.', '196.209.', '196.210.', '196.211.',
      
      // Additional Tunisia ISP ranges
      '154.126.', '154.127.', '154.128.', '154.129.',
      '160.156.', '160.157.', '160.158.', '160.159.',
    ]
  },

  // Algeria - Expanded Coverage
  DZ: {
    country: 'Algeria',
    country_code: 'DZ',
    city: 'Algiers',
    region: 'Algiers',
    timezone: 'Africa/Algiers',
    isp: 'Algeria Telecom',
    ranges: [
      // Primary Algeria ranges
      '41.100.', '41.101.', '41.102.', '41.103.', '41.104.', '41.105.',
      '41.106.', '41.107.', '41.108.', '41.109.', '41.110.', '41.111.',
      '41.112.', '41.113.', '41.114.', '41.115.', '41.116.', '41.117.',

      // AFRINIC Algeria allocations
      '105.96.', '105.97.', '105.98.', '105.99.', '105.100.', '105.101.',
      '105.102.', '105.103.', '105.104.', '105.105.', '105.106.', '105.107.',
      '105.108.', '105.109.', '105.110.', '105.111.', '105.112.', '105.113.',

      // Additional Algeria ranges
      '196.1.', '196.2.', '196.3.', '196.4.', '196.5.', '196.6.', '196.7.',
      '197.200.', '197.201.', '197.202.', '197.203.', '197.204.',
      '154.70.', '154.71.', '154.72.', '154.73.', '154.74.', '154.75.',
    ]
  },

  // Morocco - Expanded Coverage
  MA: {
    country: 'Morocco',
    country_code: 'MA',
    city: 'Casablanca',
    region: 'Casablanca-Settat',
    timezone: 'Africa/Casablanca',
    isp: 'Morocco Telecom',
    ranges: [
      // Primary Morocco ranges
      '41.248.', '41.249.', '41.250.', '41.251.', '41.252.', '41.253.',
      '41.254.', '41.255.', '105.128.', '105.129.', '105.130.', '105.131.',

      // Morocco ISP ranges
      '160.150.', '160.151.', '160.152.', '160.153.', '160.154.', '160.155.',
      '160.156.', '160.157.', '160.158.', '160.159.', '160.160.', '160.161.',

      // Additional Morocco ranges
      '196.200.', '196.201.', '196.202.', '196.203.', '196.204.',
      '197.230.', '197.231.', '197.232.', '197.233.', '197.234.',
      '154.112.', '154.113.', '154.114.', '154.115.', '154.116.',
    ]
  },

  // Saudi Arabia - New Addition
  SA: {
    country: 'Saudi Arabia',
    country_code: 'SA',
    city: 'Riyadh',
    region: 'Riyadh',
    timezone: 'Asia/Riyadh',
    isp: 'Saudi Telecom',
    ranges: [
      // Primary Saudi Arabia ranges
      '188.161.', '188.162.', '188.163.', '188.164.', '188.165.',
      '31.13.', '31.14.', '31.15.', '31.16.', '31.17.', '31.18.',

      // STC and Mobily ranges
      '109.107.', '109.108.', '109.109.', '109.110.', '109.111.',
      '212.138.', '212.139.', '212.140.', '212.141.', '212.142.',

      // Additional Saudi ranges
      '46.244.', '46.245.', '46.246.', '46.247.', '46.248.',
      '185.84.', '185.85.', '185.86.', '185.87.', '185.88.',
      '37.98.', '37.99.', '37.100.', '37.101.', '37.102.',
    ]
  },

  // UAE - New Addition
  AE: {
    country: 'United Arab Emirates',
    country_code: 'AE',
    city: 'Dubai',
    region: 'Dubai',
    timezone: 'Asia/Dubai',
    isp: 'Emirates Telecom',
    ranges: [
      // Primary UAE ranges
      '213.42.', '213.43.', '213.44.', '213.45.', '213.46.',
      '85.158.', '85.159.', '85.160.', '85.161.', '85.162.',

      // Etisalat and du ranges
      '178.135.', '178.136.', '178.137.', '178.138.', '178.139.',
      '37.252.', '37.253.', '37.254.', '37.255.',

      // Additional UAE ranges
      '46.36.', '46.37.', '46.38.', '46.39.', '46.40.',
      '185.180.', '185.181.', '185.182.', '185.183.', '185.184.',
      '94.56.', '94.57.', '94.58.', '94.59.', '94.60.',
    ]
  },

  // Germany - New Addition
  DE: {
    country: 'Germany',
    country_code: 'DE',
    city: 'Berlin',
    region: 'Berlin',
    timezone: 'Europe/Berlin',
    isp: 'Deutsche Telekom',
    ranges: [
      // Primary German ranges
      '84.', '85.', '87.', '88.', '89.', '90.', '91.', '92.',
      '93.', '94.', '95.', '176.', '178.', '188.', '217.',

      // Deutsche Telekom ranges
      '46.114.', '46.115.', '46.116.', '46.117.', '46.118.',
      '31.16.', '31.17.', '31.18.', '31.19.', '31.20.',

      // Additional German ISP ranges
      '109.43.', '109.44.', '109.45.', '109.46.', '109.47.',
      '185.48.', '185.49.', '185.50.', '185.51.', '185.52.',
      '37.201.', '37.202.', '37.203.', '37.204.', '37.205.',
    ]
  },

  // France - Expanded Coverage
  FR: {
    country: 'France',
    country_code: 'FR',
    city: 'Paris',
    region: 'Île-de-France',
    timezone: 'Europe/Paris',
    isp: 'Orange/Free/SFR',
    ranges: [
      // Primary French ranges
      '176.143.', '176.144.', '176.145.', '176.146.', '176.147.',
      '85.', '86.', '87.', '88.', '89.', '90.', '91.', '92.', '93.', '94.', '95.',

      // Orange/France Telecom ranges
      '2.', '5.', '37.', '46.', '62.', '78.', '80.', '81.', '82.', '83.', '84.',
      '109.190.', '109.191.', '109.192.', '109.193.', '109.194.',

      // Free/SFR ranges
      '212.27.', '212.28.', '212.29.', '212.30.', '212.31.',
      '185.24.', '185.25.', '185.26.', '185.27.', '185.28.',

      // Additional French ISP ranges
      '31.36.', '31.37.', '31.38.', '31.39.', '31.40.',
      '77.128.', '77.129.', '77.130.', '77.131.', '77.132.',
    ]
  },

  // United States - Comprehensive Coverage
  US: {
    country: 'United States',
    country_code: 'US',
    city: 'New York',
    region: 'New York',
    timezone: 'America/New_York',
    isp: 'Various US ISPs',
    ranges: [
      // Major US ranges
      '8.8.', '8.26.', '8.34.', '8.35.', '1.1.', '1.0.',
      '4.', '6.', '7.', '11.', '12.', '13.', '15.', '16.', '17.', '18.',
      '23.', '24.', '50.', '52.', '54.', '64.', '65.', '66.', '67.', '68.',
      '69.', '70.', '71.', '72.', '73.', '74.', '75.', '76.', '96.', '97.',
      '98.', '99.', '100.', '104.', '107.', '108.', '142.', '162.', '173.',
      '174.', '184.', '192.', '198.', '199.', '204.', '205.', '206.', '207.',
      '208.', '209.', '216.', '3.', '22.', '25.', '26.', '27.', '28.', '29.',
      '30.', '32.', '33.', '34.', '35.', '36.', '38.', '39.', '40.', '44.',
      '45.', '47.', '48.', '49.', '51.', '53.', '55.', '56.', '57.', '58.',
      '59.', '60.', '61.', '63.', '128.', '129.', '130.', '131.', '132.',
      '133.', '134.', '135.', '136.', '137.', '138.', '139.', '140.', '141.',
      '143.', '144.', '145.', '146.', '147.', '148.', '149.', '150.', '151.',
      '152.', '153.', '154.', '155.', '156.', '157.', '158.', '159.', '160.',
      '161.', '163.', '164.', '165.', '166.', '167.', '168.', '169.', '170.',
      '171.', '172.', '175.', '176.', '177.', '178.', '179.', '180.', '181.',
      '182.', '183.', '185.', '186.', '187.', '188.', '189.', '190.', '191.',
    ]
  },

  // United Kingdom - Comprehensive Coverage
  GB: {
    country: 'United Kingdom',
    country_code: 'GB',
    city: 'London',
    region: 'England',
    timezone: 'Europe/London',
    isp: 'BT/Virgin/Sky',
    ranges: [
      '2.', '5.', '25.', '31.', '37.', '46.', '51.', '62.', '77.', '78.',
      '79.', '80.', '81.', '82.', '83.', '84.', '85.', '86.', '87.', '88.',
      '89.', '90.', '91.', '92.', '93.', '94.', '95.', '109.', '141.', '151.',
      '185.', '212.', '213.', '217.', '194.', '195.', '212.', '213.', '217.',
    ]
  },

  // Canada - Comprehensive Coverage
  CA: {
    country: 'Canada',
    country_code: 'CA',
    city: 'Toronto',
    region: 'Ontario',
    timezone: 'America/Toronto',
    isp: 'Bell/Rogers/Telus',
    ranges: [
      '24.', '47.', '64.', '65.', '66.', '67.', '68.', '69.', '70.', '71.',
      '72.', '73.', '74.', '75.', '76.', '96.', '97.', '98.', '99.', '142.',
      '154.', '162.', '174.', '184.', '192.', '198.', '199.', '206.', '207.',
      '208.', '209.', '216.', '23.', '50.', '104.', '107.', '108.', '173.',
    ]
  },

  // Australia - Comprehensive Coverage
  AU: {
    country: 'Australia',
    country_code: 'AU',
    city: 'Sydney',
    region: 'New South Wales',
    timezone: 'Australia/Sydney',
    isp: 'Telstra/Optus/TPG',
    ranges: [
      '1.', '14.', '27.', '43.', '49.', '58.', '59.', '60.', '61.', '101.',
      '103.', '110.', '111.', '112.', '113.', '114.', '115.', '116.', '117.',
      '118.', '119.', '120.', '121.', '122.', '123.', '124.', '125.', '126.',
      '139.', '150.', '152.', '153.', '163.', '180.', '202.', '203.', '210.',
      '211.', '218.', '219.', '220.', '221.', '222.', '223.', '124.', '125.',
    ]
  },

  // China - Comprehensive Coverage
  CN: {
    country: 'China',
    country_code: 'CN',
    city: 'Beijing',
    region: 'Beijing',
    timezone: 'Asia/Shanghai',
    isp: 'China Telecom/Unicom',
    ranges: [
      '1.', '14.', '27.', '36.', '39.', '42.', '58.', '59.', '60.', '61.',
      '101.', '103.', '106.', '110.', '111.', '112.', '113.', '114.', '115.',
      '116.', '117.', '118.', '119.', '120.', '121.', '122.', '123.', '124.',
      '125.', '126.', '139.', '140.', '144.', '150.', '152.', '153.', '163.',
      '171.', '180.', '182.', '183.', '202.', '203.', '210.', '211.', '218.',
      '219.', '220.', '221.', '222.', '223.', '49.', '54.', '55.', '56.',
    ]
  },

  // India - Comprehensive Coverage
  IN: {
    country: 'India',
    country_code: 'IN',
    city: 'Mumbai',
    region: 'Maharashtra',
    timezone: 'Asia/Kolkata',
    isp: 'Airtel/Jio/BSNL',
    ranges: [
      '1.', '14.', '27.', '36.', '39.', '42.', '43.', '49.', '58.', '59.',
      '60.', '61.', '101.', '103.', '106.', '110.', '111.', '112.', '113.',
      '114.', '115.', '116.', '117.', '118.', '119.', '120.', '121.', '122.',
      '123.', '124.', '125.', '126.', '139.', '150.', '152.', '153.', '163.',
      '171.', '180.', '182.', '183.', '202.', '203.', '210.', '211.', '218.',
      '219.', '220.', '221.', '222.', '223.', '45.', '117.', '157.', '164.',
    ]
  },

  // Japan - Comprehensive Coverage
  JP: {
    country: 'Japan',
    country_code: 'JP',
    city: 'Tokyo',
    region: 'Tokyo',
    timezone: 'Asia/Tokyo',
    isp: 'NTT/SoftBank/KDDI',
    ranges: [
      '1.', '14.', '27.', '36.', '39.', '42.', '43.', '49.', '58.', '59.',
      '60.', '61.', '101.', '103.', '106.', '110.', '111.', '112.', '113.',
      '114.', '115.', '116.', '117.', '118.', '119.', '120.', '121.', '122.',
      '123.', '124.', '125.', '126.', '133.', '150.', '153.', '163.', '180.',
      '202.', '203.', '210.', '211.', '218.', '219.', '220.', '221.', '222.',
      '223.', '126.', '133.', '153.', '163.', '202.', '210.', '218.',
    ]
  },

  // Brazil - Comprehensive Coverage
  BR: {
    country: 'Brazil',
    country_code: 'BR',
    city: 'São Paulo',
    region: 'São Paulo',
    timezone: 'America/Sao_Paulo',
    isp: 'Vivo/Claro/TIM',
    ranges: [
      '177.', '179.', '186.', '187.', '189.', '191.', '200.', '201.', '138.',
      '143.', '144.', '146.', '147.', '148.', '150.', '152.', '155.', '156.',
      '157.', '159.', '160.', '161.', '162.', '164.', '165.', '168.', '170.',
      '177.', '179.', '186.', '187.', '189.', '191.', '200.', '201.', '45.',
    ]
  },

  // Russia - Comprehensive Coverage
  RU: {
    country: 'Russia',
    country_code: 'RU',
    city: 'Moscow',
    region: 'Moscow',
    timezone: 'Europe/Moscow',
    isp: 'Rostelecom/MTS/Beeline',
    ranges: [
      '5.', '31.', '37.', '46.', '62.', '77.', '78.', '79.', '80.', '81.',
      '82.', '83.', '84.', '85.', '86.', '87.', '88.', '89.', '90.', '91.',
      '92.', '93.', '94.', '95.', '109.', '141.', '151.', '176.', '178.',
      '185.', '188.', '212.', '213.', '217.', '194.', '195.', '46.', '91.',
    ]
  },

  // South Korea - Comprehensive Coverage
  KR: {
    country: 'South Korea',
    country_code: 'KR',
    city: 'Seoul',
    region: 'Seoul',
    timezone: 'Asia/Seoul',
    isp: 'KT/SK Telecom/LG U+',
    ranges: [
      '1.', '14.', '27.', '36.', '39.', '42.', '43.', '49.', '58.', '59.',
      '60.', '61.', '101.', '103.', '106.', '110.', '111.', '112.', '113.',
      '114.', '115.', '116.', '117.', '118.', '119.', '120.', '121.', '122.',
      '123.', '124.', '125.', '126.', '150.', '153.', '163.', '180.', '202.',
      '203.', '210.', '211.', '218.', '219.', '220.', '221.', '222.', '223.',
    ]
  },

  // Local/Private
  LO: {
    country: 'Local',
    country_code: 'LO',
    city: 'Localhost',
    region: 'Local',
    timezone: 'Local',
    isp: 'Local Network',
    ranges: [
      '127.', '192.168.', '10.', '172.16.', '172.17.', '172.18.', '172.19.',
      '172.20.', '172.21.', '172.22.', '172.23.', '172.24.', '172.25.',
      '172.26.', '172.27.', '172.28.', '172.29.', '172.30.', '172.31.',
    ]
  }
};

/**
 * Get country flag emoji from country code
 * @param {string} countryCode - Two-letter country code
 * @returns {string} Flag emoji
 */
const getCountryFlag = (countryCode) => {
  if (!countryCode || countryCode === 'UN') return '🌍';
  if (countryCode === 'LO') return '🏠';
  
  if (countryCode.length !== 2) return '🌍';
  
  const codePoints = countryCode
    .toUpperCase()
    .split('')
    .map(char => 127397 + char.charCodeAt());
  
  return String.fromCodePoint(...codePoints);
};

/**
 * Lookup IP in comprehensive database
 * @param {string} ip - IP address to lookup
 * @returns {Object|null} Country information or null if not found
 */
const lookupIPInDatabase = (ip) => {
  if (!ip) return null;
  
  // Clean IP (remove comma-separated values)
  const cleanIP = ip.includes(',') ? ip.split(',')[0].trim() : ip;
  
  // Search through all countries
  for (const [countryCode, countryData] of Object.entries(IP_RANGES)) {
    for (const range of countryData.ranges) {
      if (cleanIP.startsWith(range)) {
        return {
          country: countryData.country,
          country_code: countryData.country_code,
          city: countryData.city,
          region: countryData.region,
          timezone: countryData.timezone,
          isp: countryData.isp,
          flag: getCountryFlag(countryData.country_code),
          error: false,
          source: 'ip_database'
        };
      }
    }
  }
  
  return null; // Not found in database
};

/**
 * Test specific IP
 * @param {string} ip - IP to test
 * @returns {Object} Test result
 */
const testIP = (ip) => {
  const result = lookupIPInDatabase(ip);
  return {
    ip: ip,
    found: !!result,
    result: result,
    expected: ip.startsWith('102.157.') ? 'Tunisia' : 'varies'
  };
};

module.exports = {
  IP_RANGES,
  lookupIPInDatabase,
  getCountryFlag,
  testIP
};

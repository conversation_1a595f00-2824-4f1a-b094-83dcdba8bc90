// Test script for the enhanced analytics endpoints
// Tests both Experience and Portfolio analytics

const https = require('https');

// Configuration - try local first, then production
const LOCAL_API_URL = 'http://localhost:5000';
const PROD_API_URL = 'https://porfolio-pro-backend.onrender.com';
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = 'Adminboss';

let API_URL = PROD_API_URL; // Default to production

// Helper function to make HTTP requests
function makeRequest(options, postData = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (e) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', (e) => {
      reject(e);
    });

    if (postData) {
      req.write(postData);
    }
    req.end();
  });
}

// Test function
async function testAnalyticsEndpoints() {
  console.log('🚀 Testing Analytics Endpoints...\n');

  try {
    // Step 1: Login to get token
    console.log('1. Logging in...');
    const loginOptions = {
      hostname: 'porfolio-pro-backend.onrender.com',
      port: 443,
      path: '/api/admin/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const loginData = JSON.stringify({
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD
    });

    const loginResponse = await makeRequest(loginOptions, loginData);
    
    if (loginResponse.status !== 200) {
      throw new Error(`Login failed: ${JSON.stringify(loginResponse.data)}`);
    }

    const token = loginResponse.data.token;
    console.log('✅ Login successful');

    // Step 2: Test Experience Projects Analytics
    console.log('\n2. Testing Experience Projects Analytics...');
    const experienceOptions = {
      hostname: 'porfolio-pro-backend.onrender.com',
      port: 443,
      path: '/api/admin/experience-projects-analytics',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    const experienceResponse = await makeRequest(experienceOptions);
    
    if (experienceResponse.status === 200) {
      console.log('✅ Experience Analytics endpoint working');
      console.log(`📊 Total Projects: ${experienceResponse.data.totalProjects}`);
      console.log(`📈 Total Views: ${experienceResponse.data.summary.totalViews}`);
      
      if (experienceResponse.data.data && experienceResponse.data.data.length > 0) {
        console.log('\n📋 Experience Projects:');
        experienceResponse.data.data.forEach((project, index) => {
          console.log(`  ${index + 1}. ${project.jobTitle}`);
          console.log(`     Company: ${project.company || 'N/A'}`);
          console.log(`     Logo: ${project.logo || 'N/A'}`);
          console.log(`     Views: ${project.totalViews}`);
          console.log(`     Slug: ${project.jobSlug}`);
        });
      } else {
        console.log('⚠️  No experience project data found');
      }
    } else {
      console.log(`❌ Experience Analytics failed: ${experienceResponse.status}`);
      console.log(JSON.stringify(experienceResponse.data, null, 2));
    }

    // Step 3: Test Portfolio Projects Analytics
    console.log('\n3. Testing Portfolio Projects Analytics...');
    const portfolioOptions = {
      hostname: 'porfolio-pro-backend.onrender.com',
      port: 443,
      path: '/api/admin/portfolio-projects-analytics',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    };

    const portfolioResponse = await makeRequest(portfolioOptions);
    
    if (portfolioResponse.status === 200) {
      console.log('✅ Portfolio Analytics endpoint working');
      console.log(`📊 Total Projects: ${portfolioResponse.data.totalProjects}`);
      console.log(`📈 Total Interactions: ${portfolioResponse.data.summary.totalInteractions}`);
      console.log(`✅ Available Projects: ${portfolioResponse.data.summary.availableProjectsCount}`);
      console.log(`❌ Unavailable Projects: ${portfolioResponse.data.summary.unavailableProjectsCount}`);
      
      if (portfolioResponse.data.data && portfolioResponse.data.data.length > 0) {
        console.log('\n📋 Portfolio Projects:');
        portfolioResponse.data.data.forEach((project, index) => {
          console.log(`  ${index + 1}. ${project.projectTitle}`);
          console.log(`     ID: ${project.projectId}`);
          console.log(`     Image: ${project.projectImage || 'N/A'}`);
          console.log(`     Available: ${project.availabilityStatus ? 'Yes' : 'No'}`);
          console.log(`     Interactions: ${project.totalInteractions}`);
          console.log(`     Visitors: ${project.uniqueVisitorCount}`);
        });
      } else {
        console.log('⚠️  No portfolio project data found');
      }
    } else {
      console.log(`❌ Portfolio Analytics failed: ${portfolioResponse.status}`);
      console.log(JSON.stringify(portfolioResponse.data, null, 2));
    }

    console.log('\n🎉 Analytics endpoints test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testAnalyticsEndpoints();

// Test script for the enhanced geolocation API endpoint
// Tests the new ipinfo.io and ipwhois.io integration

const https = require('https');

// Configuration
const API_URL = 'https://porfolio-pro-backend.onrender.com'; // Update with your backend URL
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = 'Adminboss';

// Test IPs covering priority regions
const TEST_IPS = [
  '**************', // Tunisia (specific IP from requirements)
  '**************', // Tunisia (from console logs)
  '*************',  // Algeria
  '*************',  // Morocco
  '*************',  // Saudi Arabia
  '*************',  // UAE
  '************',   // Germany
  '*************',  // France
  '*******',        // US (Google DNS)
  '127.0.0.1',      // Local
  '999.999.999.999' // Invalid IP for error testing
];

/**
 * Make HTTP request
 */
const makeRequest = (options, data = null) => {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({ status: res.statusCode, data: parsed });
        } catch (error) {
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
};

/**
 * Login and get JWT token
 */
const login = async () => {
  console.log('🔐 Logging in to get JWT token...');
  
  const options = {
    hostname: API_URL.replace('https://', ''),
    path: '/api/admin/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    }
  };
  
  const loginData = {
    email: ADMIN_EMAIL,
    password: ADMIN_PASSWORD
  };
  
  try {
    const response = await makeRequest(options, loginData);
    
    if (response.status === 200 && response.data.token) {
      console.log('✅ Login successful');
      return response.data.token;
    } else {
      throw new Error(`Login failed: ${response.status} - ${JSON.stringify(response.data)}`);
    }
  } catch (error) {
    throw new Error(`Login request failed: ${error.message}`);
  }
};

/**
 * Test geolocation endpoint
 */
const testGeolocation = async (token) => {
  console.log('\n🌍 Testing geolocation endpoint...');
  
  const options = {
    hostname: API_URL.replace('https://', ''),
    path: '/api/admin/geolocation',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  };
  
  const requestData = {
    ips: TEST_IPS
  };
  
  try {
    const response = await makeRequest(options, requestData);
    
    console.log(`📊 Response Status: ${response.status}`);
    
    if (response.status === 200 && response.data.success) {
      console.log('✅ Geolocation endpoint working correctly');
      console.log(`📈 Processed ${response.data.processedCount} IPs`);
      
      // Display results for each test IP
      console.log('\n📍 Geolocation Results:');
      TEST_IPS.forEach(ip => {
        const result = response.data.data[ip];
        if (result) {
          const status = result.error ? '❌' : '✅';
          const source = result.source ? ` (${result.source})` : '';
          console.log(`${status} ${ip} → ${result.country} ${result.flag}${source}`);
          if (result.error) {
            console.log(`   Error: ${result.errorMessage}`);
          }
        } else {
          console.log(`❓ ${ip} → No result`);
        }
      });
      
      // Display monitoring stats if available
      if (response.data.monitoring) {
        console.log('\n📊 Monitoring Stats:');
        const stats = response.data.monitoring;
        console.log(`   IP Database: ${stats.ipDatabase.success} success, ${stats.ipDatabase.failure} failure`);
        console.log(`   ipinfo.io: ${stats.ipinfoApi.success} success, ${stats.ipinfoApi.failure} failure, ${stats.ipinfoApi.rateLimited} rate limited`);
        console.log(`   ipwhois.io: ${stats.ipwhoisApi.success} success, ${stats.ipwhoisApi.failure} failure, ${stats.ipwhoisApi.rateLimited} rate limited`);
        console.log(`   Cache: ${stats.cache.hits} hits, ${stats.cache.misses} misses (size: ${stats.cacheSize})`);
      }
      
      return true;
    } else {
      console.log('❌ Geolocation endpoint failed');
      console.log('Response:', JSON.stringify(response.data, null, 2));
      return false;
    }
  } catch (error) {
    console.log('❌ Geolocation request failed:', error.message);
    return false;
  }
};

/**
 * Test monitoring endpoint
 */
const testMonitoring = async (token) => {
  console.log('\n📊 Testing monitoring endpoint...');
  
  const options = {
    hostname: API_URL.replace('https://', ''),
    path: '/api/admin/geolocation-monitoring',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  };
  
  try {
    const response = await makeRequest(options);
    
    if (response.status === 200 && response.data.success) {
      console.log('✅ Monitoring endpoint working correctly');
      const monitoring = response.data.monitoring;
      
      console.log('\n📈 Detailed Monitoring Stats:');
      console.log(`   IP Database Success Rate: ${monitoring.ipDatabase.successRate}`);
      console.log(`   ipinfo.io Success Rate: ${monitoring.ipinfoApi.successRate}`);
      console.log(`   ipwhois.io Success Rate: ${monitoring.ipwhoisApi.successRate}`);
      console.log(`   Cache Hit Rate: ${monitoring.cache.hitRate}`);
      console.log(`   Total Errors: ${JSON.stringify(monitoring.errors)}`);
      
      return true;
    } else {
      console.log('❌ Monitoring endpoint failed');
      console.log('Response:', JSON.stringify(response.data, null, 2));
      return false;
    }
  } catch (error) {
    console.log('❌ Monitoring request failed:', error.message);
    return false;
  }
};

/**
 * Main test function
 */
const runTests = async () => {
  console.log('🧪 Starting Enhanced Geolocation API Tests');
  console.log('==========================================');
  
  try {
    // Step 1: Login
    const token = await login();
    
    // Step 2: Test geolocation endpoint
    const geolocationSuccess = await testGeolocation(token);
    
    // Step 3: Test monitoring endpoint
    const monitoringSuccess = await testMonitoring(token);
    
    // Summary
    console.log('\n🎯 Test Summary:');
    console.log('================');
    console.log(`Login: ✅ Success`);
    console.log(`Geolocation API: ${geolocationSuccess ? '✅ Success' : '❌ Failed'}`);
    console.log(`Monitoring API: ${monitoringSuccess ? '✅ Success' : '❌ Failed'}`);
    
    if (geolocationSuccess && monitoringSuccess) {
      console.log('\n🎉 All tests passed! Enhanced geolocation system is working correctly.');
    } else {
      console.log('\n⚠️ Some tests failed. Check the logs above for details.');
    }
    
  } catch (error) {
    console.error('❌ Test suite failed:', error.message);
  }
};

// Run the tests
runTests();
